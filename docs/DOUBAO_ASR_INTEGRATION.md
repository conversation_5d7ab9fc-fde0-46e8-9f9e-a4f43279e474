# 豆包 ASR 语音识别服务集成文档

## 概述

本文档描述了如何在小智ESP32服务器中集成豆包(DouBao) ASR语音识别服务。该实现基于豆包ASR的WebSocket协议，支持流式语音识别。

## 功能特性

- ✅ 流式语音识别
- ✅ WebSocket协议支持
- ✅ GZIP压缩
- ✅ 自动重连和错误处理
- ✅ 与现有STT服务架构无缝集成
- ❌ 单次识别（豆包ASR不支持）

## 配置要求

### 数据库配置

在 `sys_config` 表中添加豆包ASR配置：

```sql
INSERT INTO sys_config (
    userId, configType, provider, configName, configDesc,
    appId, apiKey, apiUrl, isDefault, state
) VALUES (
    1, 'stt', 'doubao', '豆包ASR', '豆包语音识别服务',
    'your-app-id', 'your-api-key', 'wss://openspeech.bytedance.com/api/v1/sauc',
    0, 'enabled'
);
```

### 配置参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `provider` | 服务提供商标识 | `doubao` |
| `appId` | 豆包ASR应用ID | `your-app-id` |
| `apiKey` | 豆包ASR API密钥 | `your-api-key` |
| `apiUrl` | WebSocket服务地址 | `wss://openspeech.bytedance.com/api/v1/sauc` |

## 技术实现

### 协议支持

实现了豆包ASR的完整WebSocket协议：

- **协议版本**: 0b0001
- **消息类型**: 
  - FULL_CLIENT_REQUEST (0b0001): 完整客户端请求
  - AUDIO_ONLY_REQUEST (0b0010): 纯音频请求
  - FULL_SERVER_RESPONSE (0b1001): 完整服务器响应
  - SERVER_ACK (0b1011): 服务器确认
  - SERVER_ERROR_RESPONSE (0b1111): 服务器错误响应

### 音频格式

- **采样率**: 16kHz
- **位深**: 16位
- **声道**: 单声道
- **格式**: PCM
- **编码**: raw

### 数据流处理

1. **初始化**: 发送完整客户端请求，包含音频格式和用户信息
2. **实时音频流**: 直接订阅 Sink 音频流，无缓冲队列，实现零延迟传输
3. **响应处理**: 解析服务器返回的识别结果
4. **结束处理**: 发送结束标志并处理最终结果

## 使用方法

### 通过工厂类获取服务

```java
@Autowired
private SttServiceFactory sttServiceFactory;

// 获取豆包ASR服务
SysConfig config = new SysConfig();
config.setProvider("doubao");
config.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
config.setAppId("your-app-id");
config.setApiKey("your-api-key");

SttService douBaoService = sttServiceFactory.getSttService(config);
```

### 流式识别

```java
// 创建音频数据流
Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

// 开始识别
String result = douBaoService.streamRecognition(audioSink);

// 发送音频数据
audioSink.tryEmitNext(audioData1);
audioSink.tryEmitNext(audioData2);
// ...
audioSink.tryEmitComplete();

System.out.println("识别结果: " + result);
```

## 错误处理

服务包含完善的错误处理机制：

- **连接失败**: 自动记录错误日志
- **识别超时**: 30秒超时保护
- **协议错误**: 解析服务器错误响应
- **资源清理**: 自动清理WebSocket连接和线程池

## 性能优化

- **零延迟音频流**: 直接订阅 Sink 音频流，移除中间队列缓冲，实现实时传输
- **虚拟线程**: 使用 Java 21 虚拟线程处理响应，减少线程开销
- **内存管理**: 及时清理音频缓冲区，无额外队列占用内存
- **连接复用**: 支持长连接和心跳保持
- **压缩传输**: 使用GZIP压缩减少网络传输
- **高效休眠**: 使用 `TimeUnit` 替代 `Thread.sleep()` 提升性能

## 日志记录

服务提供详细的日志记录：

```
DEBUG - 豆包ASR WebSocket连接已建立
DEBUG - 发送音频分段: 序号 2, 长度 3200, 是否最后一段: false
DEBUG - 识别结果: 你好世界
DEBUG - 收到最后一个响应包
```

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问豆包ASR服务
2. **音频格式**: 必须使用16kHz PCM格式的音频数据
3. **API配额**: 注意豆包ASR的API调用配额限制
4. **延迟考虑**: WebSocket连接可能存在网络延迟

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证API密钥和应用ID
   - 确认WebSocket URL正确

2. **识别结果为空**
   - 检查音频格式是否正确
   - 确认音频数据不为空
   - 查看服务器错误响应

3. **超时问题**
   - 检查网络稳定性
   - 调整超时时间配置
   - 确认音频流正常结束

### 调试建议

- 启用DEBUG日志级别查看详细信息
- 检查WebSocket连接状态
- 监控音频数据流的发送和接收
- 验证协议消息格式

## 扩展开发

如需扩展功能，可以：

1. 添加更多音频格式支持
2. 实现自定义错误处理策略
3. 增加连接池管理
4. 添加性能监控指标
