package com.xiaozhi.dialogue.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.io.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.zip.*;
import javax.sound.sampled.*;
import okhttp3.*;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.logging.HttpLoggingInterceptor.Level;
import okio.ByteString;

public class SaucWebsocketDemo {
    // 协议常量
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    private static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    private static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_WITH_SEQUENCE = 0b0011;
    private static final byte JSON = 0b0001;
    private static final byte GZIP = 0b0001;

    // 线程控制
    private static volatile boolean isRunning = true;
    private static final BlockingQueue<byte[]> responseQueue = new LinkedBlockingQueue<>();
    private static final ExecutorService executor = Executors.newFixedThreadPool(2);

    public static void main(String[] args) throws Exception {
        if (args.length != 4) {
            System.err.println("需要传入 4 个参数，依次为 url、appId、token 和 audioFilePath");
            System.exit(1);
        }

        final String url = args[0];
        final String appId = args[1];
        final String token = args[2];
        final String audioFilePath = args[3];

        // 创建WebSocket客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .pingInterval(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS) // 新增调用超时
                .build();

        Request request = new Request.Builder()
                .url(url)
                .header("X-Api-App-Key", appId)
                .header("X-Api-Access-Key", token)
                .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();

        WebSocket webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                System.out.println("===> 连接已建立, X-Tt-Logid:" + response.header("X-Tt-Logid"));
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                System.out.println("===> 收到文本消息: " + text);
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                responseQueue.offer(bytes.toByteArray());
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                System.out.println("===> 连接正在关闭: code=" + code + ", reason=" + reason);
                isRunning = false;
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                System.err.println("===> 连接失败: " + t.getMessage());
                isRunning = false;
            }
        });

        // 解析音频文件
        try (AudioInputStream ins = AudioSystem.getAudioInputStream(new File(audioFilePath))) {
            AudioFormat format = ins.getFormat();
            CountDownLatch latch = new CountDownLatch(2);

            // 启动发送线程
            executor.submit(() -> {
                try {
                    sendMessages(webSocket, ins, format);
                } catch (Exception e) {
                    System.err.println("发送线程出错: " + e.getMessage());
                    isRunning = false;
                } finally {
                    latch.countDown(); // 确保发送线程结束时计数器减少
                }
            });

            // 启动接收线程
            executor.submit(() -> {
                try {
                    processResponses(webSocket, client, ins);
                } catch (Exception e) {
                    System.err.println("接收线程出错: " + e.getMessage());
                    isRunning = false;
                } finally {
                    latch.countDown(); // 确保接收线程结束时计数器减少
                }
            });

            // 主线程等待两个子线程执行完毕
            latch.await();

        } catch (UnsupportedAudioFileException | IOException e) {
            System.err.println("无法解析音频文件: " + e.getMessage());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 关闭 WebSocket
            if (webSocket != null) {
                webSocket.close(1000, "正常关闭");
            }
            // 关闭自定义线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            // 关闭 OkHttpClient 线程池
            client.dispatcher().executorService().shutdown();
            try {
                if (!client.dispatcher().executorService().awaitTermination(30, TimeUnit.SECONDS)) {
                    client.dispatcher().executorService().shutdownNow();
                }
            } catch (InterruptedException e) {
                client.dispatcher().executorService().shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private static void sendMessages(WebSocket webSocket, AudioInputStream ins, AudioFormat format) throws Exception {
        int seq = 1;
        sendFullClientRequest(webSocket, format, seq);

        // 计算分段大小
        int segmentDurationMs = 200;
        int bytesPerFrame = (format.getSampleSizeInBits() / Byte.SIZE) * format.getChannels();
        int bytesPerSec = (int) (format.getFrameRate() * bytesPerFrame);
        int bufferSize = bytesPerSec * segmentDurationMs / 1000;
        byte[] buffer = new byte[bufferSize];
        while (isRunning) {
            try {
                int len = ins.read(buffer, 0, bufferSize);
                if (len <= 0)
                    break;
                boolean isLast = ins.available() == 0 || len < bufferSize;
                seq++; // 递增序列号
                System.out.println("发送音频分段: 序号 " + seq + ", 长度 " + len + ", 是否最后一段: " + isLast);
                sendAudioSegment(webSocket, buffer, len, isLast, isLast ? -seq : seq);
                if (isLast) {
                    break;
                }
                //
                Thread.sleep(segmentDurationMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private static void sendFullClientRequest(WebSocket webSocket, AudioFormat format, int seq) throws IOException {
        JsonObject user = new JsonObject();
        user.addProperty("uid", "test");

        JsonObject audio = new JsonObject();
        audio.addProperty("format", "pcm");
        audio.addProperty("sample_rate", (int) format.getSampleRate());
        audio.addProperty("bits", format.getSampleSizeInBits());
        audio.addProperty("channel", format.getChannels());
        audio.addProperty("codec", "raw");

        JsonObject request = new JsonObject();
        request.addProperty("model_name", "bigmodel");
        request.addProperty("enable_punc", true);

        JsonObject payload = new JsonObject();
        payload.add("user", user);
        payload.add("audio", audio);
        payload.add("request", request);

        System.out.println("发送完整客户端请求: " + payload.toString());

        String payloadStr = payload.toString();
        byte[] payloadBytes = gzipCompress(payloadStr.getBytes());
        byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
        byte[] payloadSize = intToBytes(payloadBytes.length);
        byte[] seqBytes = intToBytes(seq); // 使用传入的序列号

        byte[] fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, fullClientRequest, 0, header.length);
        System.arraycopy(seqBytes, 0, fullClientRequest, header.length, seqBytes.length);
        System.arraycopy(payloadSize, 0, fullClientRequest, header.length + seqBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, fullClientRequest, header.length + seqBytes.length + payloadSize.length,
                payloadBytes.length);
        webSocket.send(ByteString.of(fullClientRequest));
    }

    private static void sendAudioSegment(WebSocket webSocket, byte[] buffer, int len, boolean isLast, int seq) {
        byte messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
        byte[] header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, JSON, GZIP, (byte) 0);
        byte[] sequenceBytes = intToBytes(seq);
        byte[] payloadBytes = gzipCompress(buffer, len);
        byte[] payloadSize = intToBytes(payloadBytes.length);

        byte[] audioRequest = new byte[header.length + sequenceBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, audioRequest, 0, header.length);
        System.arraycopy(sequenceBytes, 0, audioRequest, header.length, sequenceBytes.length);
        System.arraycopy(payloadSize, 0, audioRequest, header.length + sequenceBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, audioRequest, header.length + sequenceBytes.length + payloadSize.length,
                payloadBytes.length);

        webSocket.send(ByteString.of(audioRequest));
    }

    private static void processResponses(WebSocket webSocket, OkHttpClient client, AudioInputStream ins) {
        while (isRunning || !responseQueue.isEmpty()) {
            try {
                byte[] response = responseQueue.poll(100, TimeUnit.MILLISECONDS);
                if (response != null) {
                    int sequence = parseResponse(response);
                    if (sequence < 0) { // 最后一个包
                        System.out.println("最后一个包 序号" + sequence + "\n");
                        isRunning = false;
                        // 关闭WebSocket连接
                        webSocket.close(1000, "正常关闭");
                        // 关闭音频输入流
                        ins.close();
                        /// 立即关闭OkHttpClient线程池
                        ExecutorService clientExecutor = client.dispatcher().executorService();
                        clientExecutor.shutdownNow();
                        try {
                            if (!clientExecutor.awaitTermination(15, TimeUnit.SECONDS)) {
                                System.err.println("OkHttp线程池未能及时关闭");
                            }
                        } catch (InterruptedException e) {
                            clientExecutor.shutdownNow();
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (IOException e) {
                System.err.println("关闭资源出错: " + e.getMessage());
            }
        }
    }

    private static int parseResponse(byte[] res) {
        if (res == null || res.length == 0) {
            return -1;
        }

        final byte num = 0b00001111;
        Map<String, Object> result = new HashMap<>();

        // 解析头部
        int protocol_version = (res[0] >> 4) & num;
        result.put("protocol_version", protocol_version);
        int header_size = res[0] & 0x0f;
        result.put("header_size", header_size);

        int message_type = (res[1] >> 4) & num;
        result.put("message_type", message_type);
        int message_type_specific_flags = res[1] & 0x0f;
        result.put("message_type_specific_flags", message_type_specific_flags);
        int serialization_method = (res[2] >> 4) & num;
        result.put("serialization_method", serialization_method);
        int message_compression = res[2] & 0x0f;
        result.put("message_compression", message_compression);
        int reserved = res[3];
        result.put("reserved", reserved);

        // 解析序列号
        byte[] temp = new byte[4];
        System.arraycopy(res, 4, temp, 0, temp.length);
        int sequence = bytesToInt(temp);

        // 解析payload
        String payloadStr = null;
        System.arraycopy(res, 8, temp, 0, temp.length);
        int payloadSize = bytesToInt(temp);
        byte[] payload = new byte[res.length - 12];
        System.arraycopy(res, 12, payload, 0, payload.length);

        // 处理不同类型的响应
        if (message_type == FULL_SERVER_RESPONSE) {
            if (message_compression == GZIP) {
                payloadStr = new String(gzipDecompress(payload));
            } else {
                payloadStr = new String(payload);
            }
            System.out.println("序号" + sequence + "完整服务器响应: " + payloadStr);
            result.put("payload_size", payloadSize);
        } else if (message_type == SERVER_ACK) {
            payloadStr = new String(payload);
            System.out.println("序号" + sequence + "服务器确认: " + payloadStr);
            result.put("payload_size", payloadSize);
        } else if (message_type == SERVER_ERROR_RESPONSE) {
            payloadStr = new String(payload);
            result.put("code", sequence);
            result.put("error_msg", payloadStr);
            System.err.println("序号" + sequence + "服务器错误: " + payloadStr);
        }

        System.out.println("响应详情: " + new Gson().toJson(result));
        return sequence;
    }

    // 辅助方法保持不变
    private static byte[] getHeader(byte messageType, byte messageTypeSpecificFlags,
            byte serialMethod, byte compressionType, byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (byte) ((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE);
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }

    private static byte[] intToBytes(int a) {
        return new byte[] {
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    private static int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) {
            throw new IllegalArgumentException("Invalid byte array for int conversion");
        }
        return ((src[0] & 0xFF) << 24)
                | ((src[1] & 0xff) << 16)
                | ((src[2] & 0xff) << 8)
                | ((src[3] & 0xff));
    }

    private static byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }

    private static byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
        return out.toByteArray();
    }

    private static byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[256];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return out.toByteArray();
    }
}