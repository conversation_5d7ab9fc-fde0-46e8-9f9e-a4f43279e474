package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.dialogue.stt.SttService;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Sinks;

@Slf4j
public class DouBaoService implements SttService {

    @Override
    public String getProviderName() {
        return "doubao";
    }

    @Override
    public String recognition(byte[] audioData) {
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        return "";
    }
}
