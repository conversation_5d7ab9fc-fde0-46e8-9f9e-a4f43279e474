package com.xiaozhi.dialogue.stt.providers;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaozhi.dialogue.stt.SttService;
import com.xiaozhi.entity.SysConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import reactor.core.publisher.Sinks;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 豆包(DouBao) ASR 语音识别服务实现
 * 基于 WebSocket 协议的流式语音识别服务
 */
@Slf4j
public class DouBaoService implements SttService {

    // 协议常量
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    private static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    private static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_WITH_SEQUENCE = 0b0011;
    private static final byte JSON = 0b0001;
    private static final byte GZIP = 0b0001;

    private static final String PROVIDER_NAME = "doubao";
    private static final int QUEUE_TIMEOUT_MS = 100;
    private static final long RECOGNITION_TIMEOUT_MS = 30000; // 30秒超时
    private static final int SEGMENT_DURATION_MS = 200; // 200ms分段

    private final String apiUrl;
    private final String appId;
    private final String apiKey;

    public DouBaoService(SysConfig config) {
        this.apiUrl = config.getApiUrl();
        this.appId = config.getAppId();
        this.apiKey = config.getApiKey();
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String recognition(byte[] audioData) {
        log.warn("豆包ASR不支持单次识别，请使用流式识别");
        return "";
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        if (apiUrl == null || appId == null || apiKey == null) {
            log.error("豆包ASR配置未设置，无法进行识别");
            return "";
        }

        BlockingQueue<byte[]> responseQueue = new LinkedBlockingQueue<>();
        AtomicBoolean isRunning = new AtomicBoolean(true);
        AtomicReference<String> finalResult = new AtomicReference<>("");
        CountDownLatch recognitionLatch = new CountDownLatch(1);
        AtomicInteger sequenceNumber = new AtomicInteger(1);

        // 创建WebSocket客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .pingInterval(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(apiUrl)
                .header("X-Api-App-Key", appId)
                .header("X-Api-Access-Key", apiKey)
                .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();

        WebSocket webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                log.debug("豆包ASR WebSocket连接已建立, X-Tt-Logid: {}", response.header("X-Tt-Logid"));

                try {
                    // 发送完整客户端请求
                    sendFullClientRequest(webSocket, sequenceNumber.get());

                    // 直接订阅音频流并实时发送
                    audioSink.asFlux().subscribe(
                        audioChunk -> {
                            if (isRunning.get() && audioChunk != null && audioChunk.length > 0) {
                                try {
                                    int seq = sequenceNumber.incrementAndGet();
                                    log.debug("实时发送音频分段: 序号 {}, 长度 {}", seq, audioChunk.length);
                                    sendAudioSegment(webSocket, audioChunk, audioChunk.length, false, seq);
                                } catch (Exception e) {
                                    log.error("发送音频分段失败", e);
                                }
                            }
                        },
                        error -> {
                            log.error("音频流处理错误", error);
                            isRunning.set(false);
                            // 发送结束标志
                            try {
                                int seq = sequenceNumber.incrementAndGet();
                                sendAudioSegment(webSocket, new byte[0], 0, true, -seq);
                            } catch (Exception e) {
                                log.error("发送结束标志失败", e);
                            }
                        },
                        () -> {
                            log.debug("音频流结束，发送最后一个分段");
                            // 发送结束标志
                            try {
                                int seq = sequenceNumber.incrementAndGet();
                                sendAudioSegment(webSocket, new byte[0], 0, true, -seq);
                            } catch (Exception e) {
                                log.error("发送结束标志失败", e);
                            }
                        }
                    );

                    // 启动响应处理线程
                    Thread.startVirtualThread(() -> {
                        try {
                            processResponses(responseQueue, isRunning, finalResult, recognitionLatch);
                        } catch (Exception e) {
                            log.error("处理响应时发生错误", e);
                            isRunning.set(false);
                        }
                    });

                } catch (Exception e) {
                    log.error("初始化WebSocket处理失败", e);
                    isRunning.set(false);
                    recognitionLatch.countDown();
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                log.debug("收到文本消息: {}", text);
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                responseQueue.offer(bytes.toByteArray());
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                log.debug("WebSocket连接正在关闭: code={}, reason={}", code, reason);
                isRunning.set(false);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                log.error("WebSocket连接失败", t);
                isRunning.set(false);
                recognitionLatch.countDown();
            }
        });

        try {
            // 等待识别完成或超时
            boolean recognized = recognitionLatch.await(RECOGNITION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!recognized) {
                log.warn("豆包ASR识别超时");
            }
        } catch (InterruptedException e) {
            log.error("识别过程被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 清理资源
            cleanup(webSocket, client, null);
        }

        return finalResult.get();
    }



    /**
     * 发送完整客户端请求
     */
    private void sendFullClientRequest(WebSocket webSocket, int seq) throws IOException {
        JsonObject user = new JsonObject();
        user.addProperty("uid", "xiaozhi_user");

        JsonObject audio = new JsonObject();
        audio.addProperty("format", "pcm");
        audio.addProperty("sample_rate", 16000); // 固定16kHz采样率
        audio.addProperty("bits", 16);
        audio.addProperty("channel", 1);
        audio.addProperty("codec", "raw");

        JsonObject request = new JsonObject();
        request.addProperty("model_name", "bigmodel");
        request.addProperty("enable_punc", true);

        JsonObject payload = new JsonObject();
        payload.add("user", user);
        payload.add("audio", audio);
        payload.add("request", request);

        log.debug("发送完整客户端请求: {}", payload.toString());

        String payloadStr = payload.toString();
        byte[] payloadBytes = gzipCompress(payloadStr.getBytes());
        byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
        byte[] payloadSize = intToBytes(payloadBytes.length);
        byte[] seqBytes = intToBytes(seq);

        byte[] fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, fullClientRequest, 0, header.length);
        System.arraycopy(seqBytes, 0, fullClientRequest, header.length, seqBytes.length);
        System.arraycopy(payloadSize, 0, fullClientRequest, header.length + seqBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, fullClientRequest, header.length + seqBytes.length + payloadSize.length,
                payloadBytes.length);

        webSocket.send(ByteString.of(fullClientRequest));
    }

    /**
     * 发送音频分段
     */
    private void sendAudioSegment(WebSocket webSocket, byte[] buffer, int len, boolean isLast, int seq) {
        byte messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
        byte[] header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, JSON, GZIP, (byte) 0);
        byte[] sequenceBytes = intToBytes(seq);
        byte[] payloadBytes = gzipCompress(buffer, len);
        byte[] payloadSize = intToBytes(payloadBytes.length);

        byte[] audioRequest = new byte[header.length + sequenceBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, audioRequest, 0, header.length);
        System.arraycopy(sequenceBytes, 0, audioRequest, header.length, sequenceBytes.length);
        System.arraycopy(payloadSize, 0, audioRequest, header.length + sequenceBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, audioRequest, header.length + sequenceBytes.length + payloadSize.length,
                payloadBytes.length);

        webSocket.send(ByteString.of(audioRequest));
    }

    /**
     * 处理响应消息
     */
    private void processResponses(BlockingQueue<byte[]> responseQueue, AtomicBoolean isRunning,
                                 AtomicReference<String> finalResult, CountDownLatch recognitionLatch) {
        StringBuilder resultBuilder = new StringBuilder();

        while (isRunning.get() || !responseQueue.isEmpty()) {
            try {
                byte[] response = responseQueue.poll(QUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                if (response != null) {
                    String text = parseResponse(response);
                    if (text != null && !text.trim().isEmpty()) {
                        resultBuilder.append(text);
                        log.debug("识别结果: {}", text);
                    }

                    // 检查是否为最后一个包
                    if (isLastPacket(response)) {
                        log.debug("收到最后一个响应包");
                        isRunning.set(false);
                        break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        finalResult.set(resultBuilder.toString().trim());
        recognitionLatch.countDown();
    }

    /**
     * 解析响应消息
     */
    private String parseResponse(byte[] res) {
        if (res == null || res.length == 0) {
            return null;
        }

        try {
            final byte num = 0b00001111;
            Map<String, Object> result = new HashMap<>();

            // 解析头部
            int protocol_version = (res[0] >> 4) & num;
            result.put("protocol_version", protocol_version);
            int header_size = res[0] & 0x0f;
            result.put("header_size", header_size);

            int message_type = (res[1] >> 4) & num;
            result.put("message_type", message_type);
            int message_type_specific_flags = res[1] & 0x0f;
            result.put("message_type_specific_flags", message_type_specific_flags);
            int serialization_method = (res[2] >> 4) & num;
            result.put("serialization_method", serialization_method);
            int message_compression = res[2] & 0x0f;
            result.put("message_compression", message_compression);
            int reserved = res[3];
            result.put("reserved", reserved);

            // 解析序列号
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int sequence = bytesToInt(temp);

            // 解析payload
            String payloadStr = null;
            System.arraycopy(res, 8, temp, 0, temp.length);
            int payloadSize = bytesToInt(temp);
            byte[] payload = new byte[res.length - 12];
            System.arraycopy(res, 12, payload, 0, payload.length);

            // 处理不同类型的响应
            if (message_type == FULL_SERVER_RESPONSE) {
                if (message_compression == GZIP) {
                    payloadStr = new String(gzipDecompress(payload));
                } else {
                    payloadStr = new String(payload);
                }
                log.debug("序号{}完整服务器响应: {}", sequence, payloadStr);

                // 解析JSON响应获取识别文本
                try {
                    Gson gson = new Gson();
                    JsonObject jsonResponse = gson.fromJson(payloadStr, JsonObject.class);
                    if (jsonResponse.has("result") && jsonResponse.get("result").isJsonObject()) {
                        JsonObject resultObj = jsonResponse.getAsJsonObject("result");
                        if (resultObj.has("text")) {
                            return resultObj.get("text").getAsString();
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析识别结果JSON失败: {}", e.getMessage());
                }

                result.put("payload_size", payloadSize);
            } else if (message_type == SERVER_ACK) {
                payloadStr = new String(payload);
                log.debug("序号{}服务器确认: {}", sequence, payloadStr);
                result.put("payload_size", payloadSize);
            } else if (message_type == SERVER_ERROR_RESPONSE) {
                payloadStr = new String(payload);
                result.put("code", sequence);
                result.put("error_msg", payloadStr);
                log.error("序号{}服务器错误: {}", sequence, payloadStr);
            }

            log.debug("响应详情: {}", new Gson().toJson(result));
            return null; // 只有FULL_SERVER_RESPONSE才返回文本
        } catch (Exception e) {
            log.error("解析响应消息失败", e);
            return null;
        }
    }

    /**
     * 检查是否为最后一个包
     */
    private boolean isLastPacket(byte[] res) {
        if (res == null || res.length < 8) {
            return false;
        }

        try {
            // 解析序列号
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int sequence = bytesToInt(temp);
            return sequence < 0; // 负序列号表示最后一个包
        } catch (Exception e) {
            log.error("检查最后包失败", e);
            return false;
        }
    }

    /**
     * 清理资源
     */
    private void cleanup(WebSocket webSocket, OkHttpClient client, ExecutorService executor) {
        try {
            // 关闭WebSocket
            if (webSocket != null) {
                webSocket.close(1000, "正常关闭");
            }

            // 如果有线程池，关闭它
            if (executor != null) {
                executor.shutdown();
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            }

            // 关闭OkHttpClient线程池
            client.dispatcher().executorService().shutdown();
            if (!client.dispatcher().executorService().awaitTermination(5, TimeUnit.SECONDS)) {
                client.dispatcher().executorService().shutdownNow();
            }
        } catch (Exception e) {
            log.error("清理资源时发生错误", e);
        }
    }

    // ==================== 协议工具方法 ====================

    /**
     * 构建协议头部
     */
    private byte[] getHeader(byte messageType, byte messageTypeSpecificFlags,
                            byte serialMethod, byte compressionType, byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (byte) ((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE);
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }

    /**
     * 整数转字节数组
     */
    private byte[] intToBytes(int a) {
        return new byte[] {
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    /**
     * 字节数组转整数
     */
    private int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) {
            throw new IllegalArgumentException("Invalid byte array for int conversion");
        }
        return ((src[0] & 0xFF) << 24)
                | ((src[1] & 0xff) << 16)
                | ((src[2] & 0xff) << 8)
                | ((src[3] & 0xff));
    }

    /**
     * GZIP压缩
     */
    private byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }

    /**
     * GZIP压缩（指定长度）
     */
    private byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
        } catch (IOException e) {
            log.error("GZIP压缩失败", e);
            return new byte[0];
        }
        return out.toByteArray();
    }

    /**
     * GZIP解压缩
     */
    private byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[256];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            log.error("GZIP解压缩失败", e);
            return null;
        }
        return out.toByteArray();
    }
}
