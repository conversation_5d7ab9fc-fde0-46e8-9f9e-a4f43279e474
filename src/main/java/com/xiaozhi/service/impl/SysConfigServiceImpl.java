package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dao.ConfigMapper;
import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.vo.ConfigQueryParams;
import com.xiaozhi.vo.ConfigUpdateParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型配置
 *
 * <AUTHOR>
 */

@Service
public class SysConfigServiceImpl extends BaseServiceImpl implements SysConfigService {
    private final static String CACHE_NAME = "XiaoZhi:SysConfig";

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private SttServiceFactory sttServiceFactory;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    /**
     * 查询配置
     *
     * @param configId 配置id
     * @return 具体的配置
     */
    @Override
    @Cacheable(value = CACHE_NAME, key = "#configId", unless = "#result == null")
    public SysConfig selectConfigById(Integer configId) {
        return configMapper.selectById(configId);
    }

    /**
     * 查询默认配置
     *
     * @param modelType
     * @return 配置
     */
    @Override
    @Cacheable(value = CACHE_NAME, key = "#modelType", unless = "#result == null")
    public SysConfig selectModelType(String modelType) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getModelType, modelType);

        var modelConfigs = configMapper.selectList(query);

        return modelConfigs.stream()
                .filter(SysConfig::getIsDefault)
                .findFirst()
                .orElseGet(modelConfigs::getFirst);
    }

    @Override
    public SysConfig findOneOf(String provider, Integer userId) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getProvider, provider)
                .eq(SysConfig::getUserId, userId);
        return configMapper.selectOne(query);
    }

    @Override
    public Resp findPage(ConfigQueryParams params) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getType, params.getType());

        var page = configMapper.selectPage(params.toPage(), query);

        return Resp.from(page);
    }

    @Override
    public Either<BizError, ?> update(Integer id, ConfigUpdateParams params) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getId, id)
                .select(SysConfig::getId, SysConfig::getType, SysConfig::getUserId, SysConfig::getIsDefault);

        return Option.of(configMapper.selectOne(query))
                .toEither(BizError.UserNotExists)
                .map(config -> {

                    if (config.getType().equals("stt")) {
                        sttServiceFactory.removeCache(config);
                    } else if (config.getType().equals("tts")) {
                        ttsServiceFactory.removeCache(config);
                    }

                    // 如果当前配置被设置为默认，则将同类型同用户的其他配置设置为非默认
                    if (!config.getIsDefault() && params.getIsDefault()) {
                        configMapper.resetDefault(config.getUserId(), config.getType());
                    }

                    configMapper.updateById(config);

                    return true;
                });
    }

    @Override
    public Either<BizError, ?> delete(Integer id) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getId, id)
                .select(SysConfig::getId, SysConfig::getType);

        return Option.of(configMapper.selectOne(query))
                .toEither(BizError.UserNotExists)
                .map(config -> {

                    if (config.getType().equals("stt")) {
                        sttServiceFactory.removeCache(config);
                    } else if (config.getType().equals("tts")) {
                        ttsServiceFactory.removeCache(config);
                    }

                    configMapper.deleteById(config);

                    return true;
                });
    }

    @Override
    public Either<BizError, ?> create(SysConfig params) {
        var query = new OhMyLambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getName, params.getName());

        return Option.when(configMapper.selectCount(query) <= 0, () -> params)
                .toEither(BizError.UserNotExists)
                .map(config -> {

                    if (params.getIsDefault()) {
                        configMapper.resetDefault(params.getUserId(), params.getType());
                    }

                    configMapper.insert(config);
                    return true;
                });
    }

}