package com.xiaozhi.communication.common;

import com.xiaozhi.communication.domain.*;
import com.xiaozhi.dialogue.domain.Sentence;
import com.xiaozhi.dialogue.llm.factory.ChatModelFactory;
import com.xiaozhi.dialogue.llm.memory.ConversationFactory;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.ToolsSessionHolder;
import com.xiaozhi.dialogue.llm.tool.mcp.McpToolRegistrationService;
import com.xiaozhi.dialogue.service.AudioService;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.dialogue.service.IotService;
import com.xiaozhi.dialogue.service.VadService;
import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.SysDevice;
import com.xiaozhi.enums.ListenState;
import com.xiaozhi.service.SysConfigService;
import com.xiaozhi.service.SysDeviceService;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

@Component
public class MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(MessageHandler.class);

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private AudioService audioService;

    @Resource
    private TtsServiceFactory ttsService;

    @Resource
    private VadService vadService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private SysConfigService configService;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private IotService iotService;

    @Resource
    private TtsServiceFactory ttsFactory;

    @Resource
    private SttServiceFactory sttFactory;

    @Autowired
    private ConversationFactory conversationFactory;

    @Resource
    private ChatModelFactory chatModelFactory;

    @Resource
    private ToolsGlobalRegistry toolsGlobalRegistry;

    @Resource
    private McpToolRegistrationService mcpToolRegistrationService;

    /**
     * 处理连接建立事件.
     *
     * @param chatSession
     * @param deviceId
     */
    public void afterConnection(ChatSession chatSession, @NotNull String deviceId) {
        var sessionId = chatSession.getSessionId();
        // 注册会话
        sessionManager.registerSession(chatSession);

        logger.info("开始查询设备信息 - DeviceId: {}", deviceId);
        var device = Optional.ofNullable(deviceService.findByDeviceId(deviceId)).orElse(new SysDevice());
        sessionManager.registerDevice(sessionId, device);

        // 从数据库获取角色描述。device.getRoleId()表示当前设备的当前活跃角色，或者上次退出时的活跃角色。
        var role = sessionManager.getCurrentRole(chatSession);
        var conversation = conversationFactory.initConversation(device, role.getRole(), sessionId);
        chatSession.setConversation(conversation);

        // 如果已绑定，则初始化其他内容
        if (device.getRoleId() != null) {
            // 这里需要放在虚拟线程外
            var toolsSessionHolder = new ToolsSessionHolder(sessionId, device, toolsGlobalRegistry);
            chatSession.setFunctionSessionHolder(toolsSessionHolder);

            // 以上同步处理结束后，再启动虚拟线程进行设备初始化，确保chatSession中已设置的sysDevice信息
            Thread.startVirtualThread(() -> {
                try {
                    if (role.getSttId() != null) {
                        var sttConfig = configService.selectConfigById(role.getSttId());
                        if (sttConfig != null) {
                            role.setSttConfig(sttConfig);
                            sttFactory.getSttService(sttConfig);// 提前初始化，加速后续使用
                        }
                    }

                    if (role.getTtsId() != null) {
                        var ttsConfig = configService.selectConfigById(role.getTtsId());
                        if (ttsConfig != null) {
                            role.setTtsConfig(ttsConfig);
                            ttsFactory.getTtsService(ttsConfig, role.getVoice());// 提前初始化，加速后续使用
                        }
                    }

                    if (role.getModelId() != null) {
                        var modelConfig = configService.selectConfigById(role.getModelId());
                        role.setModelConfig(modelConfig);
                        chatModelFactory.takeChatModel(modelConfig, sessionId, deviceId);
                    }

                    // 注册全局函数
                    toolsSessionHolder.registerGlobalFunctionTools(chatSession);

                    // 初始化MCP工具（设备端MCP和第三方MCP）
                    try {
                        logger.debug("开始初始化MCP工具");
                        mcpToolRegistrationService.initializeAllMcpTools(chatSession);
                        logger.debug("MCP工具初始化完成");
                    } catch (Exception e) {
                        logger.error("MCP工具初始化失败, 错误: {}", e.getMessage(), e);
                    }

                    // 更新设备状态
                    deviceService.update(device.getId(), new SysDevice()
                            .setIsOnline(true)
                            .setLastLoginTime(new Date()));

                } catch (Exception e) {
                    logger.error("设备初始化失败 - DeviceId: {} 错误: {}", deviceId, e.getMessage());
                    try {
                        sessionManager.closeSession(sessionId);
                    } catch (Exception ex) {
                        logger.error("关闭连接失败", ex);
                    }
                }
            });

        } else {
            handleUnboundDevice(sessionId, new SysDevice().setDeviceId(deviceId));
        }
    }

    /**
     * 处理连接关闭事件.
     *
     * @param sessionId
     */
    public void afterConnectionClosed(String sessionId) {
        var chatSession = sessionManager.getSession(sessionId);
        if (chatSession == null || !chatSession.isOpen()) {
            return;
        }
        // 连接关闭时清理资源
        var device = chatSession.getSysDevice();
        if (device != null) {
            deviceService.update(device.getId(), new SysDevice()
                    .setIsOnline(false)
                    .setLastLoginTime(new Date()));
            logger.info("WebSocket连接关闭 - SessionId: {}, DeviceId: {}", sessionId, device.getDeviceId());
        }
        // 清理会话
        sessionManager.closeSession(sessionId);
        // 清理VAD会话
        vadService.resetSession(sessionId);
        // 清理音频处理会话
        audioService.cleanupSession(sessionId);
        // 清理对话
        dialogueService.cleanupSession(sessionId);
        // 清理 Conversation 缓存的对话历史。
        var conversation = chatSession.getConversation();
        if (conversation != null) {
            conversation.clear();
        }
    }

    /**
     * 处理音频数据
     *
     * @param sessionId
     * @param opusData
     */
    public void handleBinaryMessage(String sessionId, byte[] opusData) {
        var chatSession = sessionManager.getSession(sessionId);
        if (chatSession == null || !chatSession.isOpen() || !chatSession.isAudioChannelOpen()) {
            return;
        }

        dialogueService.processAudioData(chatSession, opusData);
    }

    public void handleUnboundDevice(String sessionId, SysDevice device) {
        var chatSession = sessionManager.getSession(sessionId);
        if (device.getDeviceName() != null && device.getRoleId() == null) {
            String message = "设备未配置角色，请到角色配置页面完成配置后开始对话";

            try {
                String audioFilePath = ttsService.getDefaultTtsService().textToSpeech(message);
                Sentence sentence = new Sentence(message, audioFilePath);
                sentence.setFirst(true);
                sentence.setLast(true);
                audioService.sendAudioMessage(chatSession, sentence);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void handleListenMessage(ChatSession chatSession, ListenMessage message) {
        String sessionId = chatSession.getSessionId();
        chatSession.setMode(message.getMode());

        // 根据state处理不同的监听状态
        switch (message.getState()) {
            case ListenState.Start:
                // 开始监听，准备接收音频数据
                logger.info("开始监听 - Mode: {}", message.getMode());

                // 初始化VAD会话
                vadService.initSession(sessionId);
                chatSession.setAudioChannelOpen(true);
                break;

            case ListenState.Stop:
                // 停止监听
                logger.info("停止监听");

                // 关闭音频流
                sessionManager.completeAudioStream(sessionId);
                sessionManager.closeAudioStream(sessionId);
                sessionManager.setStreamingState(sessionId, false);
                // 重置VAD会话
                vadService.resetSession(sessionId);
                break;

            case ListenState.Text:
                // 检测聊天文本输入
                if (chatSession.isAudioPlaying()) {
                    dialogueService.abortDialogue(chatSession, message.getMode().getValue());
                }
                dialogueService.handleText(chatSession, message.getText(), true);
                break;

            case ListenState.Detect:
                // 检测到唤醒词
                dialogueService.handleWakeWord(chatSession, message.getText());
                break;

            default:
                logger.warn("未知的listen状态: {}", message.getState());
        }
    }

    private void handleAbortMessage(ChatSession session, AbortMessage message) {
        dialogueService.abortDialogue(session, message.getReason());
    }

    private void handleIotMessage(ChatSession chatSession, IotMessage message) {
        String sessionId = chatSession.getSessionId();
        logger.info("收到IoT消息 - SessionId: {}", sessionId);

        // 处理设备描述信息
        if (message.getDescriptors() != null) {
            logger.info("收到设备描述信息: {}", message.getDescriptors());
            // 处理设备描述信息的逻辑
            iotService.handleDeviceDescriptors(sessionId, message.getDescriptors());
        }

        // 处理设备状态更新
        if (message.getStates() != null) {
            logger.info("收到设备状态更新: {}", message.getStates());
            // 处理设备状态更新的逻辑
            iotService.handleDeviceStates(sessionId, message.getStates());
        }
    }

    private void handleGoodbyeMessage(ChatSession session, GoodbyeMessage message) {
        sessionManager.closeSession(session);
    }

    private void handleDeviceMcpMessage(ChatSession chatSession, DeviceMcpMessage message) {
        var mcpRequestId = message.getPayload().getId();
        var future = chatSession.getDeviceMcpHolder()
                .getMcpPendingRequests()
                .get(mcpRequestId);
        if (future != null) {
            future.complete(message);
            chatSession.getDeviceMcpHolder().getMcpPendingRequests().remove(mcpRequestId);
        }
    }

    public void handleMessage(Message msg, String sessionId) {
        var chatSession = sessionManager.getSession(sessionId);
        switch (msg) {
            case ListenMessage m -> handleListenMessage(chatSession, m);
            case IotMessage m -> handleIotMessage(chatSession, m);
            case AbortMessage m -> handleAbortMessage(chatSession, m);
            case GoodbyeMessage m -> handleGoodbyeMessage(chatSession, m);
            case DeviceMcpMessage m -> handleDeviceMcpMessage(chatSession, m);
            case CustomMessage m -> handleCustomMessage(chatSession, m);
            default -> {
            }
        }
    }

    private void handleCustomMessage(ChatSession chatSession, CustomMessage m) {
        var payload = m.getPayload();

        dialogueService.sendSentence(chatSession, payload.taskContent(), true);
    }
}
