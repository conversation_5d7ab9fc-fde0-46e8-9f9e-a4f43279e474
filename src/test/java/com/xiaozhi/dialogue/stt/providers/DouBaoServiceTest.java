package com.xiaozhi.dialogue.stt.providers;

import com.xiaozhi.entity.SysConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Sinks;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DouBaoService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class DouBaoServiceTest {

    private DouBaoService douBaoService;
    private SysConfig config;

    @BeforeEach
    void setUp() {
        config = new SysConfig();
        config.setProvider("doubao");
        config.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
        config.setAppId("test-app-id");
        config.setApiKey("test-api-key");
        
        douBaoService = new DouBaoService(config);
    }

    @Test
    void testGetProviderName() {
        assertEquals("doubao", douBaoService.getProviderName());
    }

    @Test
    void testSupportsStreaming() {
        assertTrue(douBaoService.supportsStreaming());
    }

    @Test
    void testRecognition() {
        // 豆包ASR不支持单次识别
        String result = douBaoService.recognition(new byte[]{1, 2, 3});
        assertEquals("", result);
    }

    @Test
    void testStreamRecognitionWithNullConfig() {
        // 测试配置为空的情况
        SysConfig nullConfig = new SysConfig();
        DouBaoService serviceWithNullConfig = new DouBaoService(nullConfig);
        
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
        String result = serviceWithNullConfig.streamRecognition(audioSink);
        
        assertEquals("", result);
    }

    @Test
    void testStreamRecognitionWithValidConfig() {
        // 注意：这个测试需要真实的豆包ASR服务，在实际环境中可能会失败
        // 这里主要测试方法调用不会抛出异常
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();

        // 由于没有真实的服务端点，这里会返回空字符串
        assertDoesNotThrow(() -> {
            String result = douBaoService.streamRecognition(audioSink);
            assertNotNull(result);
        });

        // 模拟发送一些音频数据（在实际使用中，这些数据会被实时处理）
        audioSink.tryEmitNext(new byte[]{1, 2, 3, 4});
        audioSink.tryEmitNext(new byte[]{5, 6, 7, 8});
        audioSink.tryEmitComplete();
    }

    @Test
    void testProtocolConstants() {
        // 测试协议常量是否正确定义
        // 这些常量应该与豆包ASR协议规范一致
        assertNotNull(douBaoService);
        assertEquals("doubao", douBaoService.getProviderName());
    }

    @Test
    void testConfigurationValidation() {
        // 测试配置验证
        SysConfig invalidConfig = new SysConfig();
        invalidConfig.setProvider("doubao");
        // 缺少必要的配置项
        
        DouBaoService serviceWithInvalidConfig = new DouBaoService(invalidConfig);
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
        
        String result = serviceWithInvalidConfig.streamRecognition(audioSink);
        assertEquals("", result);
    }
}
