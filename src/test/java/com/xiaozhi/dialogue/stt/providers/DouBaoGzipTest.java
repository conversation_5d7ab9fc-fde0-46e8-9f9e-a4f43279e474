package com.xiaozhi.dialogue.stt.providers;

import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试GZIP压缩和解压缩功能
 */
class DouBaoGzipTest {

    @Test
    void testGzipCompressEmptyData() throws IOException {
        // 测试空数据的GZIP压缩
        byte[] emptyData = new byte[0];
        byte[] compressed = gzipCompress(emptyData, 0);
        
        assertNotNull(compressed);
        assertTrue(compressed.length > 0, "压缩后的空数据应该有内容（GZIP头部）");
        
        // 验证可以正确解压缩
        byte[] decompressed = gzipDecompress(compressed);
        assertNotNull(decompressed);
        assertEquals(0, decompressed.length, "解压缩后应该是空数据");
    }

    @Test
    void testGzipCompressNormalData() throws IOException {
        // 测试正常数据的GZIP压缩
        String testData = "Hello, DouBao ASR!";
        byte[] originalData = testData.getBytes();
        byte[] compressed = gzipCompress(originalData, originalData.length);
        
        assertNotNull(compressed);
        assertTrue(compressed.length > 0, "压缩后应该有数据");
        
        // 验证可以正确解压缩
        byte[] decompressed = gzipDecompress(compressed);
        assertNotNull(decompressed);
        assertEquals(testData, new String(decompressed), "解压缩后数据应该与原始数据一致");
    }

    @Test
    void testGzipCompressAudioData() throws IOException {
        // 测试音频数据的GZIP压缩
        byte[] audioData = new byte[1024];
        // 填充一些模拟音频数据
        for (int i = 0; i < audioData.length; i++) {
            audioData[i] = (byte) (i % 256);
        }
        
        byte[] compressed = gzipCompress(audioData, audioData.length);
        
        assertNotNull(compressed);
        assertTrue(compressed.length > 0, "压缩后应该有数据");
        
        // 验证可以正确解压缩
        byte[] decompressed = gzipDecompress(compressed);
        assertNotNull(decompressed);
        assertArrayEquals(audioData, decompressed, "解压缩后音频数据应该与原始数据一致");
    }

    /**
     * GZIP压缩（指定长度）
     */
    private byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            // 对于空数据，返回一个有效的空GZIP数据
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
                // 写入空数据但确保GZIP流完整
                gzip.finish();
            } catch (IOException e) {
                throw new RuntimeException("GZIP压缩空数据失败", e);
            }
            return out.toByteArray();
        }
        
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
            gzip.finish(); // 确保所有数据被写入
        } catch (IOException e) {
            throw new RuntimeException("GZIP压缩失败", e);
        }
        return out.toByteArray();
    }

    /**
     * GZIP解压缩
     */
    private byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[256];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            throw new RuntimeException("GZIP解压缩失败", e);
        }
        return out.toByteArray();
    }
}
