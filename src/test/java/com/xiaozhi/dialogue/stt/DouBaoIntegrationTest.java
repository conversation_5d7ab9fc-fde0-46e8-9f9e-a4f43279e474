package com.xiaozhi.dialogue.stt;

import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.stt.providers.DouBaoService;
import com.xiaozhi.entity.SysConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Sinks;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DouBao ASR 集成测试
 * 测试服务在Spring Boot环境中的集成情况
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class DouBaoIntegrationTest {

    @Autowired
    private SttServiceFactory sttServiceFactory;

    @Test
    void testDouBaoServiceCreation() {
        // 创建豆包ASR配置
        SysConfig config = new SysConfig();
        config.setProvider("doubao");
        config.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
        config.setAppId("test-app-id");
        config.setApiKey("test-api-key");

        // 通过工厂创建服务
        SttService sttService = sttServiceFactory.getSttService(config);

        // 验证服务类型
        assertNotNull(sttService);
        assertTrue(sttService instanceof DouBaoService);
        assertEquals("doubao", sttService.getProviderName());
        assertTrue(sttService.supportsStreaming());
    }

    @Test
    void testDouBaoServiceBasicFunctionality() {
        // 创建豆包ASR配置
        SysConfig config = new SysConfig();
        config.setProvider("doubao");
        config.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
        config.setAppId("test-app-id");
        config.setApiKey("test-api-key");

        // 获取服务实例
        SttService sttService = sttServiceFactory.getSttService(config);

        // 测试单次识别（应该返回空字符串，因为豆包不支持）
        String result = sttService.recognition(new byte[]{1, 2, 3});
        assertEquals("", result);

        // 测试流式识别（不会连接到真实服务，但应该不抛异常）
        Sinks.Many<byte[]> audioSink = Sinks.many().multicast().onBackpressureBuffer();
        assertDoesNotThrow(() -> {
            String streamResult = sttService.streamRecognition(audioSink);
            assertNotNull(streamResult);
        });
    }

    @Test
    void testFactoryCaching() {
        // 创建相同的配置
        SysConfig config1 = new SysConfig();
        config1.setProvider("doubao");
        config1.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
        config1.setAppId("test-app-id");
        config1.setApiKey("test-api-key");

        SysConfig config2 = new SysConfig();
        config2.setProvider("doubao");
        config2.setApiUrl("wss://openspeech.bytedance.com/api/v1/sauc");
        config2.setAppId("test-app-id");
        config2.setApiKey("test-api-key");

        // 获取服务实例
        SttService service1 = sttServiceFactory.getSttService(config1);
        SttService service2 = sttServiceFactory.getSttService(config2);

        // 验证是否为同一实例（工厂缓存）
        assertNotNull(service1);
        assertNotNull(service2);
        // 注意：这里可能不是同一实例，取决于工厂的缓存策略
    }
}
