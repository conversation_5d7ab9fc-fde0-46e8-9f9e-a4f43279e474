package com.xiaozhi.dialogue.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.intent.IntentService;
import com.xiaozhi.dialogue.llm.tool.ToolsGlobalRegistry;
import com.xiaozhi.dialogue.llm.tool.mcp.device.DeviceMcpService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpService;
import com.xiaozhi.utils.JsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * processWithIntentRecognition 方法测试
 * 测试返回值的正确性：
 * - true: 意图已被处理
 * - false: 需要继续聊天
 */
@ExtendWith(MockitoExtension.class)
class ProcessWithIntentRecognitionTest {

    private static final Logger logger = LoggerFactory.getLogger(ProcessWithIntentRecognitionTest.class);

    @Mock
    private IntentService intentService;

    @Mock
    private ToolsGlobalRegistry toolsGlobalRegistry;

    @Mock
    private DeviceMcpService deviceMcpService;

    @Mock
    private ThirdPartyMcpService thirdPartyMcpService;

    @Mock
    private ChatSession chatSession;

    @InjectMocks
    private DialogueService dialogueService;

    private Method processWithIntentRecognitionMethod;

    @BeforeEach
    void setUp() throws Exception {
        // 设置基本的mock行为
        when(chatSession.getSessionId()).thenReturn("test-session-id");
        when(chatSession.isOpen()).thenReturn(true);

        // 获取私有方法
        processWithIntentRecognitionMethod = DialogueService.class.getDeclaredMethod(
                "processWithIntentRecognition", ChatSession.class, String.class);
        processWithIntentRecognitionMethod.setAccessible(true);
    }

    @Test
    void testProcessWithIntentRecognition_ContinueChat_ReturnsFalse() throws Exception {
        // 测试 continue_chat 意图返回 false
        String userText = "帮我分析一下今天的学习计划";
        String intentResult = "{\"function\": {\"name\": \"continue_chat\"}}";

        when(intentService.recognize(userText)).thenReturn(intentResult);

        // Mock JsonUtil
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            Map<String, Object> mockIntentJson = Map.of("function", Map.of("name", "continue_chat"));
            mockedJsonUtil.when(() -> JsonUtil.fromJson(intentResult, Map.class)).thenReturn(mockIntentJson);

            // 执行测试
            Boolean result = (Boolean) processWithIntentRecognitionMethod.invoke(
                    dialogueService, chatSession, userText);

            // 验证返回 false（需要继续聊天）
            assertFalse(result, "continue_chat 意图应该返回 false");

            // 验证意图识别被调用
            verify(intentService, times(1)).recognize(userText);

            logger.info("测试通过: continue_chat 意图返回 false");
        }
    }

    @Test
    void testProcessWithIntentRecognition_FunctionWithMessages_ReturnsTrue() throws Exception {
        // 测试带有成功/失败消息的函数调用
        String userText = "声音调到80%";
        String intentResult = "{\"function\": {\"name\": \"self.audio_speaker.set_volume\", \"arguments\": {\"volume\": 80}, \"success_message\": \"音量已调整到80%\", \"failure_message\": \"抱歉，音量调整失败\"}}";

        when(intentService.recognize(userText)).thenReturn(intentResult);

        // Mock JsonUtil
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            Map<String, Object> mockIntentJson = Map.of("function",
                Map.of("name", "self.audio_speaker.set_volume",
                       "arguments", Map.of("volume", 80),
                       "success_message", "音量已调整到80%",
                       "failure_message", "抱歉，音量调整失败"));
            mockedJsonUtil.when(() -> JsonUtil.fromJson(intentResult, Map.class)).thenReturn(mockIntentJson);

            // 执行测试 - 由于没有真实的工具注册，会返回失败消息
            Boolean result = (Boolean) processWithIntentRecognitionMethod.invoke(
                    dialogueService, chatSession, userText);

            // 验证返回 true（返回了失败消息，意图被处理）
            assertTrue(result, "函数调用应该返回 true（即使失败也有消息返回）");

            // 验证意图识别被调用
            verify(intentService, times(1)).recognize(userText);

            logger.info("测试通过: 带消息的函数调用返回 true");
        }
    }

    @Test
    void testProcessWithIntentRecognition_IntentRecognitionError_ReturnsFalse() throws Exception {
        // 测试意图识别错误返回 false
        String userText = "这是一个复杂的请求";
        String intentResult = "意图识别错误: 无法解析用户输入";

        when(intentService.recognize(userText)).thenReturn(intentResult);

        // 执行测试
        Boolean result = (Boolean) processWithIntentRecognitionMethod.invoke(
                dialogueService, chatSession, userText);

        // 验证返回 false（需要继续聊天）
        assertFalse(result, "意图识别错误应该返回 false");

        // 验证意图识别被调用
        verify(intentService, times(1)).recognize(userText);

        logger.info("测试通过: 意图识别错误返回 false");
    }

    @Test
    void testProcessWithIntentRecognition_Exception_ReturnsFalse() throws Exception {
        // 测试异常情况返回 false
        String userText = "测试异常情况";

        when(intentService.recognize(userText)).thenThrow(new RuntimeException("意图识别服务异常"));

        // 执行测试
        Boolean result = (Boolean) processWithIntentRecognitionMethod.invoke(
                dialogueService, chatSession, userText);

        // 验证返回 false（需要继续聊天）
        assertFalse(result, "异常情况应该返回 false");

        // 验证意图识别被调用
        verify(intentService, times(1)).recognize(userText);

        logger.info("测试通过: 异常情况返回 false");
    }

    @Test
    void testProcessWithIntentRecognition_InvalidJson_ReturnsFalse() throws Exception {
        // 测试无效JSON返回 false
        String userText = "其他类型的请求";
        String intentResult = "无效的JSON格式";

        when(intentService.recognize(userText)).thenReturn(intentResult);

        // Mock JsonUtil 抛出异常
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            mockedJsonUtil.when(() -> JsonUtil.fromJson(intentResult, Map.class))
                    .thenThrow(new RuntimeException("JSON解析失败"));

            // 执行测试
            Boolean result = (Boolean) processWithIntentRecognitionMethod.invoke(
                    dialogueService, chatSession, userText);

            // 验证返回 false（需要继续聊天）
            assertFalse(result, "无效JSON应该返回 false");

            // 验证意图识别被调用
            verify(intentService, times(1)).recognize(userText);

            logger.info("测试通过: 无效JSON返回 false");
        }
    }

    @Test
    void testReturnValueMeaning() {
        // 测试返回值含义的文档化
        logger.info("=== processWithIntentRecognition 返回值含义 ===");
        logger.info("true:  意图已被处理（如直接函数调用）");
        logger.info("false: 需要继续聊天（如 continue_chat、错误、异常等）");
        logger.info("===============================================");
        
        // 这个测试总是通过，用于文档化返回值含义
        assertTrue(true);
    }
}
