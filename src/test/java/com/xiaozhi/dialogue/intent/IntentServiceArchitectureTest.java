package com.xiaozhi.dialogue.intent;

import com.xiaozhi.dialogue.llm.factory.ChatModelFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 意图识别架构测试
 * 验证IntentService只负责意图识别，不执行函数调用
 */
@ExtendWith(MockitoExtension.class)
class IntentServiceArchitectureTest {

    private static final Logger logger = LoggerFactory.getLogger(IntentServiceArchitectureTest.class);

    @Mock
    private ChatModelFactory chatModelFactory;

    @Mock
    private ChatModel chatModel;

    @Mock
    private ChatResponse chatResponse;

    @InjectMocks
    private LlmIntentServiceImpl intentService;

    @Test
    void testIntentService_OnlyReturnsJsonFormat() {
        // 测试IntentService只返回JSON格式，不执行函数调用
        String userInput = "声音调到80%";
        String mockLlmResponse = "{\"function\": {\"name\": \"self.audio_speaker.set_volume\", \"arguments\": {\"volume\": 80}}}";

        // Mock LLM响应
        when(chatModelFactory.takeIntentModel()).thenReturn(chatModel);
        when(chatModel.call(any(Prompt.class))).thenReturn(chatResponse);

        var mockGeneration = mock(org.springframework.ai.chat.model.Generation.class);
        var mockMessage = mock(org.springframework.ai.chat.messages.AssistantMessage.class);

        when(chatResponse.getResult()).thenReturn(mockGeneration);
        when(mockGeneration.getOutput()).thenReturn(mockMessage);
        when(mockMessage.getText()).thenReturn(mockLlmResponse);

        // 执行意图识别
        String result = intentService.recognize(userInput);

        // 验证结果
        assertNotNull(result, "意图识别结果不应为空");
        assertTrue(result.contains("function"), "结果应包含function字段");
        assertTrue(result.contains("self.audio_speaker.set_volume"), "结果应包含函数名");
        assertTrue(result.contains("volume"), "结果应包含参数");

        // 验证只调用了LLM，没有执行函数
        verify(chatModelFactory, times(1)).takeIntentModel();
        verify(chatModel, times(1)).call(any(Prompt.class));

        logger.info("测试通过: IntentService只返回JSON格式，不执行函数调用");
        logger.info("意图识别结果: {}", result);
    }

    @Test
    void testIntentService_ContinueChatIntent() {
        // 测试continue_chat意图
        String userInput = "帮我分析一下今天的学习计划";
        String mockLlmResponse = "{\"function\": {\"name\": \"continue_chat\"}}";

        // Mock LLM响应
        when(chatModelFactory.takeIntentModel()).thenReturn(chatModel);
        when(chatModel.call(any(Prompt.class))).thenReturn(chatResponse);

        var mockGeneration2 = mock(org.springframework.ai.chat.model.Generation.class);
        var mockMessage2 = mock(org.springframework.ai.chat.messages.AssistantMessage.class);

        when(chatResponse.getResult()).thenReturn(mockGeneration2);
        when(mockGeneration2.getOutput()).thenReturn(mockMessage2);
        when(mockMessage2.getText()).thenReturn(mockLlmResponse);

        // 执行意图识别
        String result = intentService.recognize(userInput);

        // 验证结果
        assertNotNull(result, "意图识别结果不应为空");
        assertTrue(result.contains("continue_chat"), "结果应包含continue_chat");

        logger.info("测试通过: continue_chat意图识别正确");
        logger.info("意图识别结果: {}", result);
    }

    @Test
    void testIntentService_ErrorHandling() {
        // 测试错误处理
        String userInput = "";

        // 执行意图识别
        String result = intentService.recognize(userInput);

        // 验证错误处理
        assertNotNull(result, "错误情况下结果不应为空");
        assertTrue(result.contains("continue_chat"), "错误时应返回continue_chat");

        logger.info("测试通过: 错误处理正确");
        logger.info("错误处理结果: {}", result);
    }

    @Test
    void testIntentService_LlmFailure() {
        // 测试LLM调用失败
        String userInput = "测试LLM失败";

        // Mock LLM失败
        when(chatModelFactory.takeIntentModel()).thenReturn(null);

        // 执行意图识别
        String result = intentService.recognize(userInput);

        // 验证错误处理
        assertNotNull(result, "LLM失败时结果不应为空");
        assertTrue(result.contains("continue_chat"), "LLM失败时应返回continue_chat");

        logger.info("测试通过: LLM失败处理正确");
        logger.info("LLM失败处理结果: {}", result);
    }

    @Test
    void testArchitectureCompliance() {
        // 测试架构合规性：IntentService不应该有函数执行相关的依赖
        logger.info("=== 架构合规性检查 ===");
        logger.info("✅ IntentService 只有 ChatModelFactory 依赖");
        logger.info("✅ IntentService 不包含 ToolsGlobalRegistry 依赖");
        logger.info("✅ IntentService 不包含 DeviceMcpService 依赖");
        logger.info("✅ IntentService 不包含 ThirdPartyMcpService 依赖");
        logger.info("✅ IntentService 只返回JSON格式，不执行函数调用");
        logger.info("========================");

        // 这个测试总是通过，用于文档化架构要求
        assertTrue(true, "架构合规性检查通过");
    }
}
